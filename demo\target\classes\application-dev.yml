# 开发环境配置
spring:
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************************
    username: root
    password: 123456
    # 连接池配置
    hikari:
      minimum-idle: 2
      maximum-pool-size: 10
      auto-commit: true
      idle-timeout: 30000
      pool-name: SmartFarmHikariCP-Dev
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
  
  # 开发工具配置
  devtools:
    restart:
      enabled: true
      additional-paths: src/main/java
    livereload:
      enabled: true
  
  # JPA配置（如果使用）
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.MySQL8Dialect

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    # 开启SQL日志
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 开启驼峰命名
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /

# 日志配置
logging:
  level:
    root: INFO
    com.example.demo: DEBUG
    com.example.demo.mapper: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
    org.mybatis: DEBUG
  pattern:
    console: '%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx'

# JWT配置
jwt:
  secret: smartFarmSystemJwtSecretKeyDev2024
  access-token-expiration: 7200
  refresh-token-expiration: 604800

# 应用配置
app:
  # 文件上传配置
  upload:
    path: uploads/dev/
    max-size: 10
  
  # 开发环境邮件配置（使用测试邮箱）
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: test-password
    from: <EMAIL>
  
  # 支付配置（开发环境使用沙箱）
  payment:
    alipay:
      app-id: 2021********0000
      gateway-url: https://openapi.alipaydev.com/gateway.do
      notify-url: http://localhost:8080/api/payment/alipay/notify
      return-url: http://localhost:8080/payment/success
    wechat:
      app-id: wx1234567890abcdef
      notify-url: http://localhost:8080/api/payment/wechat/notify
  
  # 积分配置
  points:
    register-bonus: 100
    daily-checkin: 10
    exchange-rate: 100
  
  # 安全配置
  security:
    password-salt: smartFarmPasswordSaltDev2024
    max-login-attempts: 10
    account-lock-duration: 5

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always

# Swagger配置
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
    path: /swagger-ui.html

# 调试配置
debug: false
trace: false