package com.example.demo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.demo.entity.ProductCategory;
import com.example.demo.mapper.ProductCategoryMapper;
import com.example.demo.service.ProductCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商品分类服务实现类
 */
@Service
@Transactional
public class ProductCategoryServiceImpl extends ServiceImpl<ProductCategoryMapper, ProductCategory> implements ProductCategoryService {

    private static final Logger logger = LoggerFactory.getLogger(ProductCategoryServiceImpl.class);

    @Autowired
    private ProductCategoryMapper categoryMapper;

    @Override
    public boolean createCategory(ProductCategory category) {
        try {
            // 检查分类编码是否已存在
            if (StringUtils.hasText(category.getCode()) && existsByCode(category.getCode())) {
                logger.warn("分类编码已存在: " + category.getCode());
                return false;
            }

            // 检查分类名称是否已存在
            if (existsByName(category.getName())) {
                logger.warn("分类名称已存在: {}", category.getName());
                return false;
            }

            // 设置默认值
            category.setStatus(1); // 启用状态
            category.setCreatedAt(LocalDateTime.now());
            category.setUpdatedAt(LocalDateTime.now());

            return save(category);
        } catch (Exception e) {
            logger.error("创建商品分类失败", e);
            return false;
        }
    }

    @Override
    public boolean updateCategory(ProductCategory category) {
        try {
            // 检查分类是否存在
            ProductCategory existingCategory = getById(category.getCategoryId());
            if (existingCategory == null) {
                logger.warn("分类不存在: {}", category.getCategoryId());
                return false;
            }

            // 检查分类编码是否重复（排除自己）
            if (StringUtils.hasText(category.getCode())) {
                QueryWrapper<ProductCategory> codeWrapper = new QueryWrapper<>();
                codeWrapper.eq("code", category.getCode())
                          .ne("category_id", category.getCategoryId());
                if (count(codeWrapper) > 0) {
                    logger.warn("分类编码已存在: {}", category.getCode());
                    return false;
                }
            }

            // 检查分类名称是否重复（排除自己）
            QueryWrapper<ProductCategory> nameWrapper = new QueryWrapper<>();
            nameWrapper.eq("name", category.getName())
                      .ne("category_id", category.getCategoryId());
            if (count(nameWrapper) > 0) {
                logger.warn("分类名称已存在: {}", category.getName());
                return false;
            }

            category.setUpdatedAt(LocalDateTime.now());
            return updateById(category);
        } catch (Exception e) {
            logger.error("更新商品分类失败", e);
            return false;
        }
    }

    @Override
    public boolean deleteCategory(Long categoryId) {
        try {
            // 检查是否有子分类
            if (hasChildren(categoryId)) {
                logger.warn("分类下存在子分类，无法删除: {}", categoryId);
                return false;
            }

            // 检查是否有商品
            if (hasProducts(categoryId)) {
                logger.warn("分类下存在商品，无法删除: {}", categoryId);
                return false;
            }

            return removeById(categoryId);
        } catch (Exception e) {
            logger.error("删除商品分类失败", e);
            return false;
        }
    }

    @Override
    public ProductCategory findById(Long categoryId) {
        return getById(categoryId);
    }

    @Override
    public ProductCategory findByCode(String code) {
        return categoryMapper.findByCode(code);
    }

    @Override
    public ProductCategory findByName(String name) {
        return categoryMapper.findByName(name);
    }

    @Override
    public List<ProductCategory> getAllCategories() {
        QueryWrapper<ProductCategory> wrapper = new QueryWrapper<>();
        wrapper.orderByAsc("sort_order", "create_time");
        return list(wrapper);
    }

    @Override
    public List<ProductCategory> getEnabledCategories() {
        return categoryMapper.getEnabledCategories();
    }

    @Override
    public List<ProductCategory> getCategoriesByStatus(Integer status) {
        return categoryMapper.getCategoriesByStatus(status);
    }

    @Override
    public List<ProductCategory> getParentCategories() {
        QueryWrapper<ProductCategory> wrapper = new QueryWrapper<>();
        wrapper.isNull("parent_id").or().eq("parent_id", 0)
               .eq("status", 1)
               .orderByAsc("sort_order", "create_time");
        return list(wrapper);
    }

    @Override
    public List<ProductCategory> getChildCategories(Long parentId) {
        QueryWrapper<ProductCategory> wrapper = new QueryWrapper<>();
        wrapper.eq("parent_id", parentId)
               .eq("status", 1)
               .orderByAsc("sort_order", "create_time");
        return list(wrapper);
    }

    @Override
    public List<ProductCategory> buildCategoryTree() {
        try {
            // 获取所有启用的分类
            List<ProductCategory> allCategories = getEnabledCategories();
            
            // 构建分类树
            return buildTree(allCategories, null);
        } catch (Exception e) {
            logger.error("构建分类树失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 递归构建分类树
     */
    private List<ProductCategory> buildTree(List<ProductCategory> allCategories, Long parentId) {
        return allCategories.stream()
                .filter(category -> {
                    if (parentId == null) {
                        return category.getParentId() == null || category.getParentId() == 0;
                    } else {
                        return parentId.equals(category.getParentId());
                    }
                })
                .map(category -> {
                    List<ProductCategory> children = buildTree(allCategories, category.getCategoryId());
                    category.setChildren(children);
                    return category;
                })
                .collect(Collectors.toList());
    }

    @Override
    public boolean updateCategoryStatus(Long categoryId, Integer status) {
        try {
            ProductCategory category = new ProductCategory();
            category.setCategoryId(categoryId);
            category.setStatus(status);
            category.setUpdatedAt(LocalDateTime.now());
            return updateById(category);
        } catch (Exception e) {
            logger.error("更新分类状态失败", e);
            return false;
        }
    }

    @Override
    public boolean existsByCode(String code) {
        QueryWrapper<ProductCategory> wrapper = new QueryWrapper<>();
        wrapper.eq("code", code);
        return count(wrapper) > 0;
    }

    @Override
    public boolean existsByName(String name) {
        QueryWrapper<ProductCategory> wrapper = new QueryWrapper<>();
        wrapper.eq("name", name);
        return count(wrapper) > 0;
    }

    @Override
    public boolean hasChildren(Long categoryId) {
        QueryWrapper<ProductCategory> wrapper = new QueryWrapper<>();
        wrapper.eq("parent_id", categoryId);
        return count(wrapper) > 0;
    }

    @Override
    public boolean hasProducts(Long categoryId) {
        // 这里需要查询商品表，暂时返回false
        // 在实际项目中需要注入ProductMapper来查询
        return false;
    }

    @Override
    public boolean batchDeleteCategories(List<Long> categoryIds) {
        try {
            // 检查每个分类是否可以删除
            for (Long categoryId : categoryIds) {
                if (hasChildren(categoryId) || hasProducts(categoryId)) {
                    logger.warn("分类{}下存在子分类或商品，无法删除", categoryId);
                    return false;
                }
            }
            
            return removeByIds(categoryIds);
        } catch (Exception e) {
            logger.error("批量删除分类失败", e);
            return false;
        }
    }

    @Override
    public Object getCategoryStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // 总分类数
            statistics.put("totalCategories", count());
            
            // 启用分类数
            QueryWrapper<ProductCategory> enabledWrapper = new QueryWrapper<>();
            enabledWrapper.eq("status", 1);
            statistics.put("enabledCategories", count(enabledWrapper));
            
            // 禁用分类数
            QueryWrapper<ProductCategory> disabledWrapper = new QueryWrapper<>();
            disabledWrapper.eq("status", 0);
            statistics.put("disabledCategories", count(disabledWrapper));
            
            // 父级分类数
            QueryWrapper<ProductCategory> parentWrapper = new QueryWrapper<>();
            parentWrapper.isNull("parent_id").or().eq("parent_id", 0);
            statistics.put("parentCategories", count(parentWrapper));
            
            // 子分类数
            QueryWrapper<ProductCategory> childWrapper = new QueryWrapper<>();
            childWrapper.isNotNull("parent_id").ne("parent_id", 0);
            statistics.put("childCategories", count(childWrapper));
            
            return statistics;
        } catch (Exception e) {
            logger.error("获取分类统计信息失败", e);
            return new HashMap<>();
        }
    }
}