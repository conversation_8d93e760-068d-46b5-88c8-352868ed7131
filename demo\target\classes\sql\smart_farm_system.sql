-- 智能助农销售系统数据库建表语句
-- 数据库：smart_farm_system
-- 字符集：utf8mb4
-- 排序规则：utf8mb4_unicode_ci

CREATE DATABASE IF NOT EXISTS smart_farm_system DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE smart_farm_system;

-- 1. 用户信息表
CREATE TABLE `sys_user` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `password` VARCHAR(255) NOT NULL COMMENT '密码',
    `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    `real_name` VARCHAR(50) DEFAULT NULL COMMENT '真实姓名',
    `avatar` VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
    `gender` TINYINT(1) DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
    `birthday` DATE DEFAULT NULL COMMENT '生日',
    `address` VARCHAR(255) DEFAULT NULL COMMENT '地址',
    `role` VARCHAR(20) NOT NULL DEFAULT 'consumer' COMMENT '角色：consumer-消费者，farmer-农户，admin-管理员',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    `auth_status` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '认证状态：0-未认证，1-认证中，2-已认证，3-认证失败',
    `last_login_time` DATETIME DEFAULT NULL COMMENT '最后登录时间',
    `last_login_ip` VARCHAR(50) DEFAULT NULL COMMENT '最后登录IP',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_email` (`email`),
    UNIQUE KEY `uk_phone` (`phone`),
    KEY `idx_role` (`role`),
    KEY `idx_status` (`status`),
    KEY `idx_auth_status` (`auth_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

-- 2. 商品分类表
CREATE TABLE `product_category` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
    `code` VARCHAR(50) NOT NULL COMMENT '分类编码',
    `name` VARCHAR(100) NOT NULL COMMENT '分类名称',
    `icon` VARCHAR(255) DEFAULT NULL COMMENT '分类图标',
    `sort_order` INT(11) DEFAULT 0 COMMENT '排序',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品分类表';

-- 3. 商品信息表
CREATE TABLE `product` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
    `name` VARCHAR(200) NOT NULL COMMENT '商品名称',
    `description` TEXT COMMENT '商品描述',
    `category_id` BIGINT(20) NOT NULL COMMENT '分类ID',
    `price` DECIMAL(10,2) NOT NULL COMMENT '价格',
    `unit` VARCHAR(20) NOT NULL DEFAULT 'kg' COMMENT '单位',
    `stock` INT(11) NOT NULL DEFAULT 0 COMMENT '库存数量',
    `images` JSON COMMENT '商品图片JSON数组',
    `specifications` JSON COMMENT '商品规格JSON对象',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：0-下架，1-待审核，2-上架',
    `farmer_id` BIGINT(20) NOT NULL COMMENT '农户ID',
    `sales` INT(11) NOT NULL DEFAULT 0 COMMENT '销量',
    `rating` DECIMAL(3,2) DEFAULT 0.00 COMMENT '评分',
    `review_count` INT(11) DEFAULT 0 COMMENT '评价数量',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_farmer_id` (`farmer_id`),
    KEY `idx_status` (`status`),
    KEY `idx_price` (`price`),
    KEY `idx_sales` (`sales`),
    CONSTRAINT `fk_product_category` FOREIGN KEY (`category_id`) REFERENCES `product_category` (`id`),
    CONSTRAINT `fk_product_farmer` FOREIGN KEY (`farmer_id`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品信息表';

-- 4. 购物车表
CREATE TABLE `shopping_cart` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '购物车ID',
    `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
    `product_id` BIGINT(20) NOT NULL COMMENT '商品ID',
    `quantity` INT(11) NOT NULL DEFAULT 1 COMMENT '数量',
    `selected` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否选中：0-未选中，1-选中',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_product` (`user_id`, `product_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_product_id` (`product_id`),
    CONSTRAINT `fk_cart_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`),
    CONSTRAINT `fk_cart_product` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购物车表';

-- 5. 订单表
CREATE TABLE `order_info` (
    `id` VARCHAR(50) NOT NULL COMMENT '订单ID',
    `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '订单状态：1-待支付，2-已支付，3-已发货，4-已完成，5-已取消',
    `total_amount` DECIMAL(10,2) NOT NULL COMMENT '商品总金额',
    `delivery_fee` DECIMAL(10,2) DEFAULT 0.00 COMMENT '配送费',
    `discount_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠金额',
    `payable_amount` DECIMAL(10,2) NOT NULL COMMENT '应付金额',
    `payment_method` VARCHAR(20) DEFAULT NULL COMMENT '支付方式：alipay-支付宝，wechat-微信，points-积分',
    `delivery_method` VARCHAR(20) NOT NULL COMMENT '配送方式：express-快递，pickup-自提',
    `delivery_name` VARCHAR(50) DEFAULT NULL COMMENT '收货人姓名',
    `delivery_phone` VARCHAR(20) DEFAULT NULL COMMENT '收货人电话',
    `delivery_address` VARCHAR(500) DEFAULT NULL COMMENT '收货地址',
    `remark` VARCHAR(500) DEFAULT NULL COMMENT '订单备注',
    `cancel_reason` VARCHAR(200) DEFAULT NULL COMMENT '取消原因',
    `paid_at` DATETIME DEFAULT NULL COMMENT '支付时间',
    `shipped_at` DATETIME DEFAULT NULL COMMENT '发货时间',
    `completed_at` DATETIME DEFAULT NULL COMMENT '完成时间',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`),
    CONSTRAINT `fk_order_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 6. 订单商品表
CREATE TABLE `order_item` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '订单商品ID',
    `order_id` VARCHAR(50) NOT NULL COMMENT '订单ID',
    `product_id` BIGINT(20) NOT NULL COMMENT '商品ID',
    `product_name` VARCHAR(200) NOT NULL COMMENT '商品名称',
    `product_image` VARCHAR(255) DEFAULT NULL COMMENT '商品图片',
    `price` DECIMAL(10,2) NOT NULL COMMENT '商品单价',
    `quantity` INT(11) NOT NULL COMMENT '购买数量',
    `subtotal` DECIMAL(10,2) NOT NULL COMMENT '小计金额',
    `farmer_id` BIGINT(20) NOT NULL COMMENT '农户ID',
    `farmer_name` VARCHAR(50) NOT NULL COMMENT '农户名称',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_farmer_id` (`farmer_id`),
    CONSTRAINT `fk_order_item_order` FOREIGN KEY (`order_id`) REFERENCES `order_info` (`id`),
    CONSTRAINT `fk_order_item_product` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`),
    CONSTRAINT `fk_order_item_farmer` FOREIGN KEY (`farmer_id`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单商品表';

-- 7. 积分账户表
CREATE TABLE `points_account` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '积分账户ID',
    `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
    `total_points` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '总积分',
    `available_points` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '可用积分',
    `frozen_points` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '冻结积分',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_id` (`user_id`),
    CONSTRAINT `fk_points_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分账户表';

-- 8. 积分交易记录表
CREATE TABLE `points_transaction` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '交易记录ID',
    `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
    `transaction_type` TINYINT(1) NOT NULL COMMENT '交易类型：1-充值，2-消费，3-退款，4-奖励',
    `amount` DECIMAL(10,2) NOT NULL COMMENT '交易金额',
    `balance_before` DECIMAL(10,2) NOT NULL COMMENT '交易前余额',
    `balance_after` DECIMAL(10,2) NOT NULL COMMENT '交易后余额',
    `description` VARCHAR(200) NOT NULL COMMENT '交易描述',
    `related_order_id` VARCHAR(50) DEFAULT NULL COMMENT '关联订单ID',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：1-成功，2-失败',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_transaction_type` (`transaction_type`),
    KEY `idx_related_order_id` (`related_order_id`),
    KEY `idx_created_at` (`created_at`),
    CONSTRAINT `fk_points_transaction_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分交易记录表';

-- 9. 支付记录表
CREATE TABLE `payment_record` (
    `id` VARCHAR(50) NOT NULL COMMENT '支付记录ID',
    `order_id` VARCHAR(50) NOT NULL COMMENT '订单ID',
    `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
    `payment_method` VARCHAR(20) NOT NULL COMMENT '支付方式',
    `amount` DECIMAL(10,2) NOT NULL COMMENT '支付金额',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '支付状态：1-待支付，2-支付成功，3-支付失败',
    `third_party_id` VARCHAR(100) DEFAULT NULL COMMENT '第三方支付流水号',
    `paid_at` DATETIME DEFAULT NULL COMMENT '支付时间',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    CONSTRAINT `fk_payment_order` FOREIGN KEY (`order_id`) REFERENCES `order_info` (`id`),
    CONSTRAINT `fk_payment_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付记录表';

-- 10. 系统消息表
CREATE TABLE `system_message` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '消息ID',
    `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
    `type` VARCHAR(20) NOT NULL COMMENT '消息类型：order-订单，system-系统，promotion-促销',
    `title` VARCHAR(200) NOT NULL COMMENT '消息标题',
    `content` TEXT NOT NULL COMMENT '消息内容',
    `read_status` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '阅读状态：0-未读，1-已读',
    `read_at` DATETIME DEFAULT NULL COMMENT '阅读时间',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_type` (`type`),
    KEY `idx_read_status` (`read_status`),
    KEY `idx_created_at` (`created_at`),
    CONSTRAINT `fk_message_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统消息表';

-- 11. 系统配置表
CREATE TABLE `system_config` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
    `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
    `config_value` TEXT COMMENT '配置值',
    `config_type` VARCHAR(20) NOT NULL DEFAULT 'string' COMMENT '配置类型：string-字符串，number-数字，boolean-布尔，json-JSON',
    `description` VARCHAR(200) DEFAULT NULL COMMENT '配置描述',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 插入初始数据

-- 插入商品分类
INSERT INTO `product_category` (`code`, `name`, `icon`, `sort_order`) VALUES
('fruits', '水果', 'icon-fruits', 1),
('vegetables', '蔬菜', 'icon-vegetables', 2),
('grains', '粮食', 'icon-grains', 3),
('meat', '肉类', 'icon-meat', 4),
('dairy', '乳制品', 'icon-dairy', 5),
('seafood', '海鲜', 'icon-seafood', 6);

-- 插入管理员用户
INSERT INTO `sys_user` (`username`, `password`, `email`, `real_name`, `role`, `status`, `auth_status`) VALUES
('admin', '$2a$10$7JB720yubVSOfvVWdBYoOeymQjjYvpyTI/QCr/OcWqaHlAGJfQJTa', '<EMAIL>', '系统管理员', 'admin', 1, 2);

-- 插入系统配置
INSERT INTO `system_config` (`config_key`, `config_value`, `config_type`, `description`) VALUES
('system.name', '智能助农销售系统', 'string', '系统名称'),
('system.version', '1.0.0', 'string', '系统版本'),
('upload.max_image_size', '5242880', 'number', '图片上传最大大小（字节）'),
('upload.allowed_image_types', '["jpg", "jpeg", "png", "gif"]', 'json', '允许上传的图片类型'),
('points.exchange_rate', '1.0', 'number', '积分兑换比例（1元=多少积分）'),
('order.auto_confirm_days', '7', 'number', '订单自动确认天数');