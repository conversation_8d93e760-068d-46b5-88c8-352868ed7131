package com.example.demo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.demo.entity.User;
import com.example.demo.mapper.UserMapper;
import com.example.demo.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户服务实现类
 */
@Service
@Transactional
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public boolean register(User user) {
        try {
            // 检查用户名是否已存在
            if (existsByUsername(user.getUsername())) {
                logger.warn("用户名已存在: {}", user.getUsername());
                return false;
            }

            // 检查邮箱是否已存在
            if (StringUtils.hasText(user.getEmail()) && existsByEmail(user.getEmail())) {
                logger.warn("邮箱已存在: {}", user.getEmail());
                return false;
            }

            // 检查手机号是否已存在
            if (StringUtils.hasText(user.getPhone()) && existsByPhone(user.getPhone())) {
                logger.warn("手机号已存在: {}", user.getPhone());
                return false;
            }

            // 加密密码
            user.setPassword(passwordEncoder.encode(user.getPassword()));
            
            // 设置默认值
            user.setStatus(1); // 启用状态
            user.setCreatedAt(LocalDateTime.now());
            user.setUpdatedAt(LocalDateTime.now());
            
            // 如果是农户角色，设置认证状态为待审核
            if ("FARMER".equals(user.getRole())) {
                user.setAuthStatus(0); // 待审核
            }

            return save(user);
        } catch (Exception e) {
            logger.error("用户注册失败", e);
            return false;
        }
    }

    @Override
    public User login(String username, String password) {
        try {
            User user = userMapper.findByUsername(username);
            if (user == null) {
                user = userMapper.findByEmail(username);
            }
            if (user == null) {
                user = userMapper.findByPhone(username);
            }

            if (user != null && passwordEncoder.matches(password, user.getPassword())) {
                // 检查用户状态
                if (user.getStatus() == 0) {
                    logger.warn("用户已被禁用: {}", username);
                    return null;
                }
                
                // 更新最后登录时间
                updateLastLogin(user.getUserId());
                return user;
            }
            
            logger.warn("用户登录失败: {}", username);
            return null;
        } catch (Exception e) {
            logger.error("用户登录异常", e);
            return null;
        }
    }

    @Override
    public User findByUsername(String username) {
        return userMapper.findByUsername(username);
    }

    @Override
    public User findByEmail(String email) {
        return userMapper.findByEmail(email);
    }

    @Override
    public User findByPhone(String phone) {
        return userMapper.findByPhone(phone);
    }

    @Override
    public User findById(Long userId) {
        return getById(userId);
    }

    @Override
    public boolean updateUser(User user) {
        try {
            user.setUpdatedAt(LocalDateTime.now());
            return updateById(user);
        } catch (Exception e) {
            logger.error("更新用户信息失败", e);
            return false;
        }
    }

    @Override
    public boolean updatePassword(Long userId, String oldPassword, String newPassword) {
        try {
            User user = getById(userId);
            if (user == null) {
                logger.warn("用户不存在: {}", userId);
                return false;
            }

            // 验证旧密码
            if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
                logger.warn("旧密码错误: {}", userId);
                return false;
            }

            // 更新密码
            user.setPassword(passwordEncoder.encode(newPassword));
            user.setUpdatedAt(LocalDateTime.now());
            return updateById(user);
        } catch (Exception e) {
            logger.error("更新密码失败", e);
            return false;
        }
    }

    @Override
    public boolean resetPassword(String email, String newPassword) {
        try {
            User user = findByEmail(email);
            if (user == null) {
                logger.warn("邮箱不存在: {}", email);
                return false;
            }

            user.setPassword(passwordEncoder.encode(newPassword));
            user.setUpdatedAt(LocalDateTime.now());
            return updateById(user);
        } catch (Exception e) {
            logger.error("重置密码失败", e);
            return false;
        }
    }

    @Override
    public boolean updateLastLogin(Long userId) {
        try {
            userMapper.updateLastLogin(userId, LocalDateTime.now(), "127.0.0.1");
            return true;
        } catch (Exception e) {
            logger.error("更新最后登录时间失败", e);
            return false;
        }
    }

    @Override
    public boolean updateLastLoginTime(Long userId) {
        return updateLastLogin(userId);
    }

    @Override
    public User getUserById(Long userId) {
        return getById(userId);
    }

    @Override
    public IPage<User> getUserList(Page<User> page, String keyword, String role, Integer status) {
        return userMapper.getUserList(page, keyword, role, status);
    }

    @Override
    public List<User> getUsersByRole(String role) {
        return userMapper.getUsersByRole(role);
    }

    @Override
    public List<User> getPendingFarmers() {
        return userMapper.getPendingFarmers();
    }

    @Override
    public boolean auditFarmer(Long userId, Integer status, String remark) {
        try {
            userMapper.updateAuthStatus(userId, status);
            return true;
        } catch (Exception e) {
            logger.error("审核农户失败", e);
            return false;
        }
    }

    @Override
    public boolean updateUserStatus(Long userId, Integer status) {
        try {
            User user = new User();
            user.setId(userId);
            user.setStatus(status);
            user.setUpdatedAt(LocalDateTime.now());
            return updateById(user);
        } catch (Exception e) {
            logger.error("更新用户状态失败", e);
            return false;
        }
    }

    @Override
    public boolean deleteUser(Long userId) {
        try {
            return removeById(userId);
        } catch (Exception e) {
            logger.error("删除用户失败", e);
            return false;
        }
    }

    @Override
    public boolean batchDeleteUsers(List<Long> userIds) {
        try {
            return removeByIds(userIds);
        } catch (Exception e) {
            logger.error("批量删除用户失败", e);
            return false;
        }
    }

    @Override
    public boolean existsByUsername(String username) {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.eq("username", username);
        return count(wrapper) > 0;
    }

    @Override
    public boolean existsByEmail(String email) {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.eq("email", email);
        return count(wrapper) > 0;
    }

    @Override
    public boolean existsByPhone(String phone) {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.eq("phone", phone);
        return count(wrapper) > 0;
    }

    @Override
    public Object getUserStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // 总用户数
            statistics.put("totalUsers", count());
            
            // 各角色用户数
            QueryWrapper<User> consumerWrapper = new QueryWrapper<>();
            consumerWrapper.eq("role", "CONSUMER");
            statistics.put("consumerCount", count(consumerWrapper));
            
            QueryWrapper<User> farmerWrapper = new QueryWrapper<>();
            farmerWrapper.eq("role", "FARMER");
            statistics.put("farmerCount", count(farmerWrapper));
            
            QueryWrapper<User> adminWrapper = new QueryWrapper<>();
            adminWrapper.eq("role", "ADMIN");
            statistics.put("adminCount", count(adminWrapper));
            
            // 待审核农户数
            QueryWrapper<User> pendingWrapper = new QueryWrapper<>();
            pendingWrapper.eq("role", "FARMER").eq("auth_status", 0);
            statistics.put("pendingFarmerCount", count(pendingWrapper));
            
            // 活跃用户数（状态为1）
            QueryWrapper<User> activeWrapper = new QueryWrapper<>();
            activeWrapper.eq("status", 1);
            statistics.put("activeUserCount", count(activeWrapper));
            
            return statistics;
        } catch (Exception e) {
            logger.error("获取用户统计信息失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public boolean isUserActiveById(Long userId) {
        try {
            User user = getById(userId);
            return user != null && user.getStatus() == 1;
        } catch (Exception e) {
            logger.error("检查用户激活状态失败，用户ID: {}", userId, e);
            return false;
        }
    }

    @Override
    public User createUser(User user) {
        try {
            user.setCreatedAt(LocalDateTime.now());
            user.setUpdatedAt(LocalDateTime.now());
            save(user);
            return user;
        } catch (Exception e) {
            logger.error("创建用户失败", e);
            throw new RuntimeException("创建用户失败");
        }
    }
}