package com.example.demo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.entity.User;
import java.util.List;

/**
 * 用户服务接口
 * 提供用户管理相关的业务逻辑
 */
public interface UserService {

    /**
     * 用户注册
     * @param user 用户信息
     * @return 注册结果
     */
    boolean register(User user);

    /**
     * 用户登录
     * @param username 用户名/邮箱/手机号
     * @param password 密码
     * @return 用户信息（登录成功）或null（登录失败）
     */
    User login(String username, String password);

    /**
     * 根据用户名查询用户
     * @param username 用户名
     * @return 用户信息
     */
    User findByUsername(String username);

    /**
     * 根据邮箱查询用户
     * @param email 邮箱
     * @return 用户信息
     */
    User findByEmail(String email);

    /**
     * 根据手机号查询用户
     * @param phone 手机号
     * @return 用户信息
     */
    User findByPhone(String phone);

    /**
     * 根据ID查询用户
     * @param userId 用户ID
     * @return 用户信息
     */
    User findById(Long userId);

    /**
     * 更新用户信息
     * @param user 用户信息
     * @return 更新结果
     */
    boolean updateUser(User user);

    /**
     * 更新用户密码
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 更新结果
     */
    boolean updatePassword(Long userId, String oldPassword, String newPassword);

    /**
     * 重置密码
     * @param email 邮箱
     * @param newPassword 新密码
     * @return 重置结果
     */
    boolean resetPassword(String email, String newPassword);

    /**
     * 更新最后登录信息
     * @param userId 用户ID
     * @return 更新结果
     */
    boolean updateLastLogin(Long userId);

    /**
     * 更新最后登录时间
     * @param userId 用户ID
     * @return 更新结果
     */
    boolean updateLastLoginTime(Long userId);

    /**
     * 根据用户ID获取用户信息
     * @param userId 用户ID
     * @return 用户信息
     */
    User getUserById(Long userId);

    /**
     * 分页查询用户列表
     * @param page 分页参数
     * @param keyword 搜索关键词
     * @param role 角色筛选
     * @param status 状态筛选
     * @return 用户分页列表
     */
    IPage<User> getUserList(Page<User> page, String keyword, String role, Integer status);

    /**
     * 根据角色查询用户列表
     * @param role 角色
     * @return 用户列表
     */
    List<User> getUsersByRole(String role);

    /**
     * 查询待审核农户列表
     * @return 待审核农户列表
     */
    List<User> getPendingFarmers();

    /**
     * 审核农户认证
     * @param userId 用户ID
     * @param status 审核状态（1-通过，2-拒绝）
     * @param remark 审核备注
     * @return 审核结果
     */
    boolean auditFarmer(Long userId, Integer status, String remark);

    /**
     * 启用/禁用用户
     * @param userId 用户ID
     * @param status 状态（1-启用，0-禁用）
     * @return 操作结果
     */
    boolean updateUserStatus(Long userId, Integer status);

    /**
     * 删除用户
     * @param userId 用户ID
     * @return 删除结果
     */
    boolean deleteUser(Long userId);

    /**
     * 批量删除用户
     * @param userIds 用户ID列表
     * @return 删除结果
     */
    boolean batchDeleteUsers(List<Long> userIds);

    /**
     * 检查用户名是否存在
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     * @param email 邮箱
     * @return 是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 检查手机号是否存在
     * @param phone 手机号
     * @return 是否存在
     */
    boolean existsByPhone(String phone);

    /**
     * 获取用户统计信息
     * @return 统计信息
     */
    Object getUserStatistics();

    /**
     * 检查用户是否激活
     * @param userId 用户ID
     * @return 是否激活
     */
    boolean isUserActiveById(Long userId);

    /**
     * 创建用户
     * @param user 用户信息
     * @return 创建的用户
     */
    User createUser(User user);
}