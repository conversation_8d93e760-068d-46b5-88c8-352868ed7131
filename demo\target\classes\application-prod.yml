# 生产环境配置
spring:
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************
    username: ${DB_USERNAME:prod_user}
    password: ${DB_PASSWORD:prod_password}
    # 连接池配置
    hikari:
      minimum-idle: 10
      maximum-pool-size: 50
      auto-commit: true
      idle-timeout: 30000
      pool-name: SmartFarmHikariCP-Prod
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
      leak-detection-threshold: 60000
  
  # Redis配置
  redis:
    host: ${REDIS_HOST:your-prod-redis-host}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:your-redis-password}
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 20
        max-wait: -1ms
        max-idle: 10
        min-idle: 5
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        format_sql: false
        dialect: org.hibernate.dialect.MySQL8Dialect

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    # 生产环境关闭SQL日志
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 服务器配置
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /
  # 生产环境Tomcat优化
  tomcat:
    uri-encoding: UTF-8
    max-threads: 500
    min-spare-threads: 50
    max-connections: 10000
    accept-count: 200
    connection-timeout: 20000
    max-http-post-size: 52428800

# 日志配置
logging:
  level:
    root: WARN
    com.example.demo: INFO
    com.example.demo.mapper: WARN
    org.springframework.security: WARN
  pattern:
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
  file:
    name: /var/log/smart-farm-system/application.log
    max-size: 500MB
    max-history: 60
  logback:
    rollingpolicy:
      max-file-size: 500MB
      total-size-cap: 10GB

# JWT配置
jwt:
  secret: ${JWT_SECRET:your-production-jwt-secret-key-must-be-very-long-and-secure}
  access-token-expiration: ${JWT_ACCESS_EXPIRATION:3600}
  refresh-token-expiration: ${JWT_REFRESH_EXPIRATION:604800}

# 应用配置
app:
  # 文件上传配置
  upload:
    path: ${UPLOAD_PATH:/var/uploads/smart-farm/}
    max-size: 10
  
  # 生产环境邮件配置
  mail:
    host: ${MAIL_HOST:smtp.your-domain.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:your-mail-password}
    from: ${MAIL_FROM:<EMAIL>}
  
  # 支付配置（生产环境）
  payment:
    alipay:
      app-id: ${ALIPAY_APP_ID:your-production-alipay-app-id}
      private-key: ${ALIPAY_PRIVATE_KEY:your-alipay-private-key}
      public-key: ${ALIPAY_PUBLIC_KEY:your-alipay-public-key}
      gateway-url: https://openapi.alipay.com/gateway.do
      notify-url: ${ALIPAY_NOTIFY_URL:https://your-domain.com/api/payment/alipay/notify}
      return-url: ${ALIPAY_RETURN_URL:https://your-domain.com/payment/success}
    wechat:
      app-id: ${WECHAT_APP_ID:your-wechat-app-id}
      mch-id: ${WECHAT_MCH_ID:your-wechat-mch-id}
      api-key: ${WECHAT_API_KEY:your-wechat-api-key}
      notify-url: ${WECHAT_NOTIFY_URL:https://your-domain.com/api/payment/wechat/notify}
  
  # 短信配置
  sms:
    aliyun:
      access-key-id: ${SMS_ACCESS_KEY_ID:your-sms-access-key-id}
      access-key-secret: ${SMS_ACCESS_KEY_SECRET:your-sms-access-key-secret}
      sign-name: ${SMS_SIGN_NAME:智慧农场}
      template-code: ${SMS_TEMPLATE_CODE:SMS_123456789}
  
  # 积分配置
  points:
    register-bonus: 100
    daily-checkin: 10
    exchange-rate: 100
  
  # 安全配置
  security:
    password-salt: ${PASSWORD_SALT:your-production-password-salt}
    max-login-attempts: 5
    account-lock-duration: 30
  
  # 业务配置
  business:
    order-auto-cancel-minutes: 30
    order-auto-confirm-days: 7
    stock-warning-threshold: 10

# 管理端点配置（生产环境限制暴露）
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
  security:
    enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# Swagger配置（生产环境可选择关闭）
springdoc:
  api-docs:
    enabled: ${SWAGGER_ENABLED:false}
  swagger-ui:
    enabled: ${SWAGGER_ENABLED:false}

# 性能优化配置
spring:
  jpa:
    properties:
      hibernate:
        jdbc:
          batch_size: 50
        order_inserts: true
        order_updates: true
        batch_versioned_data: true
  
  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 3600000
      cache-null-values: false
  
  # 压缩配置
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

# 调试配置（生产环境关闭）
debug: false
trace: false