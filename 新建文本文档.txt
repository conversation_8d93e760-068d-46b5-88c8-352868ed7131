[ERROR] COMPILATION ERROR :
[INFO] -------------------------------------------------------------
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/exception/GlobalExceptionHandler.java:[222,30] 已在类 com.example.demo.exception.GlobalExceptionHandler中定义了方法 handleAccessDeniedException(org.springframework.security.access.AccessDeniedException,javax.servlet.http.HttpServletRequest)
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[48,24] 找不到符号
  符号:   方法 getStatus()
  位置: 类型为com.example.demo.entity.Product的变量 product
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[63,47] 找不到符号
  符号:   方法 getQuantity()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 existingCart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[68,55] 找不到符号        
  符号:   方法 getCartId()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 existingCart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[72,21] 找不到符号        
  符号:   方法 setUserId(java.lang.Long)
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[73,21] 找不到符号
  符号:   方法 setProductId(java.lang.Long)
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[74,21] 找不到符号        
  符号:   方法 setQuantity(java.lang.Integer)
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[75,21] 找不到符号        
  符号:   方法 setSelected(boolean)
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[76,21] 找不到符号
  符号:   方法 setCreateTime(java.time.LocalDateTime)
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[77,21] 找不到符号        
  符号:   方法 setUpdateTime(java.time.LocalDateTime)
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[100,48] 找不到符号       
  符号:   方法 getProductId()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[101,67] 找不到符号
  符号:   方法 getProductId()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[105,17] 找不到符号       
  符号:   方法 setQuantity(java.lang.Integer)
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[106,17] 找不到符号       
  符号:   方法 setUpdateTime(java.time.LocalDateTime)
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[122,43] 找不到符号
  符号:   方法 getCartId()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[142,30] 找不到符号       
  符号:   方法 removeUserCartProduct(java.lang.Long,java.lang.Long)
  位置: 类型为com.example.demo.mapper.ShoppingCartMapper的变量 cartMapper
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[162,30] 找不到符号       
  符号:   方法 clearUserCart(java.lang.Long)
  位置: 类型为com.example.demo.mapper.ShoppingCartMapper的变量 cartMapper
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[171,26] 找不到符号
  符号:   方法 getUserCartList(java.lang.Long)
  位置: 类型为com.example.demo.mapper.ShoppingCartMapper的变量 cartMapper
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[176,26] 找不到符号
  符号:   方法 getUserSelectedCartList(java.lang.Long)
  位置: 类型为com.example.demo.mapper.ShoppingCartMapper的变量 cartMapper
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[181,26] 找不到符号       
  符号:   方法 getUserCartProduct(java.lang.Long,java.lang.Long)
  位置: 类型为com.example.demo.mapper.ShoppingCartMapper的变量 cartMapper
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[188,17] 找不到符号       
  符号:   方法 setCartId(java.lang.Long)
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[189,17] 找不到符号
  符号:   方法 setSelected(java.lang.Boolean)
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[190,17] 找不到符号       
  符号:   方法 setUpdateTime(java.time.LocalDateTime)
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[206,40] 找不到符号
  符号:   方法 getCartId()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[220,17] 找不到符号       
  符号:   方法 setSelected(java.lang.Boolean)
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[221,17] 找不到符号       
  符号:   方法 setUpdateTime(java.time.LocalDateTime)
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[237,17] 找不到符号       
  符号:   方法 setSelected(java.lang.Boolean)
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[238,17] 找不到符号       
  符号:   方法 setUpdateTime(java.time.LocalDateTime)
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[250,30] 找不到符号
  符号:   方法 removeUserSelectedCart(java.lang.Long)
  位置: 类型为com.example.demo.mapper.ShoppingCartMapper的变量 cartMapper
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[259,26] 找不到符号       
  符号:   方法 getUserCartCount(java.lang.Long)
  位置: 类型为com.example.demo.mapper.ShoppingCartMapper的变量 cartMapper
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[264,26] 找不到符号
  符号:   方法 getUserSelectedCartCount(java.lang.Long)
  位置: 类型为com.example.demo.mapper.ShoppingCartMapper的变量 cartMapper
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[281,80] 找不到符号       
  符号:   方法 getProductId()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 tempCart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[284,51] 找不到符号       
  符号:   方法 getQuantity()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 existingCart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[284,76] 找不到符号       
  符号:   方法 getQuantity()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 tempCart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[285,52] 找不到符号       
  符号:   方法 getCartId()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 existingCart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[288,47] 找不到符号
  符号:   方法 getProductId()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 tempCart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[288,72] 找不到符号       
  符号:   方法 getQuantity()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 tempCart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[306,39] 找不到符号
  符号:   方法 getProduct()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[310,45] 找不到符号       
  符号:   方法 getCartId()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[311,48] 找不到符号       
  符号:   方法 getProductId()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[314,35] 找不到符号
  符号:   方法 getStatus()
  位置: 类型为com.example.demo.entity.Product的变量 product
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[315,45] 找不到符号       
  符号:   方法 getCartId()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[316,48] 找不到符号
  符号:   方法 getProductId()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[317,53] 找不到符号       
  符号:   方法 getName()
  位置: 类型为com.example.demo.entity.Product的变量 product
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[320,35] 找不到符号       
  符号:   方法 getStock()
  位置: 类型为com.example.demo.entity.Product的变量 product
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[320,53] 找不到符号       
  符号:   方法 getQuantity()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[321,45] 找不到符号       
  符号:   方法 getCartId()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[322,48] 找不到符号       
  符号:   方法 getProductId()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[323,53] 找不到符号
  符号:   方法 getName()
  位置: 类型为com.example.demo.entity.Product的变量 product
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[324,54] 找不到符号       
  符号:   方法 getQuantity()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[325,56] 找不到符号
  符号:   方法 getStock()
  位置: 类型为com.example.demo.entity.Product的变量 product
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[354,60] 方法引用无效     
  找不到符号
    符号:   方法 getQuantity()
    位置: 类 com.example.demo.entity.ShoppingCart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[358,71] 方法引用无效
  找不到符号
    符号:   方法 getQuantity()
    位置: 类 com.example.demo.entity.ShoppingCart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[369,41] 找不到符号       
  符号:   方法 getProduct()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[370,95] 找不到符号
  符号:   方法 getQuantity()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[370,38] 找不到符号       
  符号:   方法 getProduct()
  位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[371,46] 不兼容的类型: 方 
法引用无效
    对于add(java.lang.Object,java.lang.Object), 找不到合适的方法
        方法 java.math.BigDecimal.add(java.math.BigDecimal,java.math.MathContext)不适用
          (参数不匹配; java.lang.Object无法转换为java.math.BigDecimal)
        方法 java.math.BigDecimal.add(long,long)不适用
          (参数不匹配; java.lang.Object无法转换为long)
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/entity/User.java:[210,16] 找不到符号
  符号:   变量 idCard
  位置: 类 com.example.demo.entity.User
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/entity/User.java:[214,13] 找不到符号
  符号: 变量 idCard
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/entity/User.java:[218,16] 找不到符号
  符号:   变量 birthDate
  位置: 类 com.example.demo.entity.User
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/entity/User.java:[222,13] 找不到符号
  符号: 变量 birthDate
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/SmartFarmSystemApplication.java:[49,13] 找不到符号
  符号:   变量 log
  位置: 类 com.example.demo.SmartFarmSystemApplication
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/SmartFarmSystemApplication.java:[70,13] 找不到符号
  符号:   变量 log
  位置: 类 com.example.demo.SmartFarmSystemApplication
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/SmartFarmSystemApplication.java:[93,13] 找不到符号
  符号:   变量 log
  位置: 类 com.example.demo.SmartFarmSystemApplication
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/SmartFarmSystemApplication.java:[103,9] 找不到符号
  符号:   变量 log
  位置: 类 com.example.demo.SmartFarmSystemApplication
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/SmartFarmSystemApplication.java:[118,13] 找不到符号
  符号:   变量 log
  位置: 类 com.example.demo.SmartFarmSystemApplication
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/SmartFarmSystemApplication.java:[131,13] 找不到符号
  符号:   变量 log
  位置: 类 com.example.demo.SmartFarmSystemApplication
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[41,20] 无法将接口 org.apache.ibatis.logging.Log中的方法 warn应用到给定类型;
  需要: java.lang.String
  找到: java.lang.String,java.lang.String
  原因: 实际参数列表和形式参数列表长度不同
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[47,20] 无法将接口 org.apache.ibatis.logging.Log中的方法 warn应用到给定类型;
  需要: java.lang.String
  找到: java.lang.String,java.lang.String
  原因: 实际参数列表和形式参数列表长度不同
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[53,20] 无法将接口 org.apache.ibatis.logging.Log中的方法 warn应用到给定类型;
  需要: java.lang.String
  找到: java.lang.String,java.lang.String
  原因: 实际参数列表和形式参数列表长度不同
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[62,17] 找不到符号
  符号:   方法 setCreateTime(java.time.LocalDateTime)
  位置: 类型为com.example.demo.entity.User的变量 user
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[63,17] 找不到符号
  符号:   方法 setUpdateTime(java.time.LocalDateTime)
  位置: 类型为com.example.demo.entity.User的变量 user
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[91,24] 无法将接口 org.apache.ibatis.logging.Log中的方法 warn应用到给定类型;
  需要: java.lang.String
  找到: java.lang.String,java.lang.String
  原因: 实际参数列表和形式参数列表长度不同
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[96,37] 找不到符号
  符号:   方法 getUserId()
  位置: 类型为com.example.demo.entity.User的变量 user
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[100,16] 无法将接口 org.apache.ibatis.logging.Log中的方法 warn应用到给定类型;
  需要: java.lang.String
  找到: java.lang.String,java.lang.String
  原因: 实际参数列表和形式参数列表长度不同
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[131,17] 找不到符号
  符号:   方法 setUpdateTime(java.time.LocalDateTime)
  位置: 类型为com.example.demo.entity.User的变量 user
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[144,20] 无法将接口 org.apache.ibatis.logging.Log中的方法 warn应用到给定类型;
  需要: java.lang.String
  找到: java.lang.String,java.lang.Long
  原因: 实际参数列表和形式参数列表长度不同
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[150,20] 无法将接口 org.apache.ibatis.logging.Log中的方法 warn应用到给定类型;
  需要: java.lang.String
  找到: java.lang.String,java.lang.Long
  原因: 实际参数列表和形式参数列表长度不同
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[156,17] 找不到符号
  符号:   方法 setUpdateTime(java.time.LocalDateTime)
  位置: 类型为com.example.demo.entity.User的变量 user
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[169,20] 无法将接口 org.apache.ibatis.logging.Log中的方法 warn应用到给定类型;
  需要: java.lang.String
  找到: java.lang.String,java.lang.String
  原因: 实际参数列表和形式参数列表长度不同
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[174,17] 找不到符号
  符号:   方法 setUpdateTime(java.time.LocalDateTime)
  位置: 类型为com.example.demo.entity.User的变量 user
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[185,30] 无法将接口 com.example.demo.mapper.UserMapper中的方法 updateLastLogin应用到给定类型;
  需要: java.lang.Long,java.time.LocalDateTime,java.lang.String
  找到: java.lang.Long,java.time.LocalDateTime
  原因: 实际参数列表和形式参数列表长度不同
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[194,26] 找不到符号
  符号:   方法 getUserList(com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.example.demo.entity.User>,java.lang.String,java.lang.String,java.lang.Integer)
  位置: 类型为com.example.demo.mapper.UserMapper的变量 userMapper
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[199,26] 找不到符号
  符号:   方法 getUsersByRole(java.lang.String)
  位置: 类型为com.example.demo.mapper.UserMapper的变量 userMapper
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[204,26] 找不到符号
  符号:   方法 getPendingFarmers()
  位置: 类型为com.example.demo.mapper.UserMapper的变量 userMapper
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[210,30] 无法将接口 com.example.demo.mapper.UserMapper中的方法 updateAuthStatus应用到给定类型;
  需要: java.lang.Long,java.lang.Integer
  找到: java.lang.Long,java.lang.Integer,java.lang.String,java.time.LocalDateTime
  原因: 实际参数列表和形式参数列表长度不同
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[316,16] 对于error(java.lang.String,java.lang.Long,java.lang.Exception), 找不到合适的方法
    方法 org.apache.ibatis.logging.Log.error(java.lang.String,java.lang.Throwable)不适用
      (实际参数列表和形式参数列表长度不同)
    方法 org.apache.ibatis.logging.Log.error(java.lang.String)不适用
      (实际参数列表和形式参数列表长度不同)
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/config/JwtAuthenticationFilter.java:[70,25] 找不到符号
  符号:   变量 log
  位置: 类 com.example.demo.config.JwtAuthenticationFilter
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/config/JwtAuthenticationFilter.java:[72,25] 找不到符号
  符号:   变量 log
  位置: 类 com.example.demo.config.JwtAuthenticationFilter
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/config/JwtAuthenticationFilter.java:[78,17] 找不到符号
  符号:   变量 log
  位置: 类 com.example.demo.config.JwtAuthenticationFilter
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/config/JwtAuthenticationFilter.java:[83,13] 找不到符号
  符号:   变量 log
  位置: 类 com.example.demo.config.JwtAuthenticationFilter
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/config/SecurityConfig.java:[91,17] 找不到符号
  符号: 变量 log
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/config/SecurityConfig.java:[113,13] 找不到符号
  符号:   变量 log
  位置: 类 com.example.demo.config.SecurityConfig
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/config/SecurityConfig.java:[159,17] 无法将类 org.springframework.security.config.annotation.web.AbstractRequestMatcherRegistry<C>中的方法 requestMatchers应用到给定类型;
  需要: org.springframework.security.web.util.matcher.RequestMatcher[]
  找到: java.lang.String
  原因: varargs 不匹配; java.lang.String无法转换为org.springframework.security.web.util.matcher.RequestMatcher
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/controller/AuthController.java:[89,24] 找不到符号
  符号:   方法 updateLastLoginTime(java.lang.Long)
  位置: 类型为com.example.demo.service.UserService的变量 userService
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/controller/AuthController.java:[275,36] 找不到符号
  符号:   方法 getUserById(java.lang.Long)
  位置: 类型为com.example.demo.service.UserService的变量 userService
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ProductCategoryServiceImpl.java:[63,64] 找不到符号
  符号:   方法 getCategoryId()
  位置: 类型为com.example.demo.entity.ProductCategory的变量 category
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ProductCategoryServiceImpl.java:[65,47] 找不到符号     
  符号:   方法 getCategoryId()
  位置: 类型为com.example.demo.entity.ProductCategory的变量 category
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ProductCategoryServiceImpl.java:[73,54] 找不到符号
  符号:   方法 getCategoryId()
  位置: 类型为com.example.demo.entity.ProductCategory的变量 category
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ProductCategoryServiceImpl.java:[75,24] 无法将接口 org.apache.ibatis.logging.Log中的方法 warn应用到给定类型;
  需要: java.lang.String
  找到: java.lang.String,java.lang.String
  原因: 实际参数列表和形式参数列表长度不同
[INFO] 100 errors
[INFO] -------------------------------------------------------------
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  4.400 s
[INFO] Finished at: 2025-09-03T19:45:27+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.apache.maven.plugins:maven-compiler-plugin:3.8.1:compile (default-compile) on project smart-farm-system: Compilation failure: Compilation failure: 
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/exception/GlobalExceptionHandler.java:[222,30] 已在类 com.example.demo.exception.GlobalExceptionHandler中定义了方法 handleAccessDeniedException(org.springframework.security.access.AccessDeniedException,javax.servlet.http.HttpServletRequest)
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[48,24] 找不到符号        
[ERROR]   符号:   方法 getStatus()
[ERROR]   位置: 类型为com.example.demo.entity.Product的变量 product
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[63,47] 找不到符号        
[ERROR]   符号:   方法 getQuantity()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 existingCart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[68,55] 找不到符号        
[ERROR]   符号:   方法 getCartId()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 existingCart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[72,21] 找不到符号        
[ERROR]   符号:   方法 setUserId(java.lang.Long)
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[73,21] 找不到符号
[ERROR]   符号:   方法 setProductId(java.lang.Long)
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[74,21] 找不到符号        
[ERROR]   符号:   方法 setQuantity(java.lang.Integer)
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[75,21] 找不到符号        
[ERROR]   符号:   方法 setSelected(boolean)
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[76,21] 找不到符号        
[ERROR]   符号:   方法 setCreateTime(java.time.LocalDateTime)
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[77,21] 找不到符号        
[ERROR]   符号:   方法 setUpdateTime(java.time.LocalDateTime)
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[100,48] 找不到符号
[ERROR]   符号:   方法 getProductId()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[101,67] 找不到符号       
[ERROR]   符号:   方法 getProductId()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[105,17] 找不到符号       
[ERROR]   符号:   方法 setQuantity(java.lang.Integer)
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[106,17] 找不到符号
[ERROR]   符号:   方法 setUpdateTime(java.time.LocalDateTime)
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[122,43] 找不到符号       
[ERROR]   符号:   方法 getCartId()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[142,30] 找不到符号
[ERROR]   符号:   方法 removeUserCartProduct(java.lang.Long,java.lang.Long)
[ERROR]   位置: 类型为com.example.demo.mapper.ShoppingCartMapper的变量 cartMapper
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[162,30] 找不到符号       
[ERROR]   符号:   方法 clearUserCart(java.lang.Long)
[ERROR]   位置: 类型为com.example.demo.mapper.ShoppingCartMapper的变量 cartMapper
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[171,26] 找不到符号       
[ERROR]   符号:   方法 getUserCartList(java.lang.Long)
[ERROR]   位置: 类型为com.example.demo.mapper.ShoppingCartMapper的变量 cartMapper
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[176,26] 找不到符号
[ERROR]   符号:   方法 getUserSelectedCartList(java.lang.Long)
[ERROR]   位置: 类型为com.example.demo.mapper.ShoppingCartMapper的变量 cartMapper
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[181,26] 找不到符号       
[ERROR]   符号:   方法 getUserCartProduct(java.lang.Long,java.lang.Long)
[ERROR]   位置: 类型为com.example.demo.mapper.ShoppingCartMapper的变量 cartMapper
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[188,17] 找不到符号       
[ERROR]   符号:   方法 setCartId(java.lang.Long)
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[189,17] 找不到符号
[ERROR]   符号:   方法 setSelected(java.lang.Boolean)
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[190,17] 找不到符号       
[ERROR]   符号:   方法 setUpdateTime(java.time.LocalDateTime)
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[206,40] 找不到符号       
[ERROR]   符号:   方法 getCartId()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[220,17] 找不到符号       
[ERROR]   符号:   方法 setSelected(java.lang.Boolean)
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[221,17] 找不到符号       
[ERROR]   符号:   方法 setUpdateTime(java.time.LocalDateTime)
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[237,17] 找不到符号
[ERROR]   符号:   方法 setSelected(java.lang.Boolean)
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[238,17] 找不到符号       
[ERROR]   符号:   方法 setUpdateTime(java.time.LocalDateTime)
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[250,30] 找不到符号       
[ERROR]   符号:   方法 removeUserSelectedCart(java.lang.Long)
[ERROR]   位置: 类型为com.example.demo.mapper.ShoppingCartMapper的变量 cartMapper
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[259,26] 找不到符号
[ERROR]   符号:   方法 getUserCartCount(java.lang.Long)
[ERROR]   位置: 类型为com.example.demo.mapper.ShoppingCartMapper的变量 cartMapper
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[264,26] 找不到符号       
[ERROR]   符号:   方法 getUserSelectedCartCount(java.lang.Long)
[ERROR]   位置: 类型为com.example.demo.mapper.ShoppingCartMapper的变量 cartMapper
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[281,80] 找不到符号       
[ERROR]   符号:   方法 getProductId()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 tempCart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[284,51] 找不到符号       
[ERROR]   符号:   方法 getQuantity()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 existingCart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[284,76] 找不到符号       
[ERROR]   符号:   方法 getQuantity()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 tempCart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[285,52] 找不到符号
[ERROR]   符号:   方法 getCartId()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 existingCart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[288,47] 找不到符号       
[ERROR]   符号:   方法 getProductId()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 tempCart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[288,72] 找不到符号       
[ERROR]   符号:   方法 getQuantity()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 tempCart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[306,39] 找不到符号       
[ERROR]   符号:   方法 getProduct()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[310,45] 找不到符号       
[ERROR]   符号:   方法 getCartId()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[311,48] 找不到符号
[ERROR]   符号:   方法 getProductId()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[314,35] 找不到符号       
[ERROR]   符号:   方法 getStatus()
[ERROR]   位置: 类型为com.example.demo.entity.Product的变量 product
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[315,45] 找不到符号       
[ERROR]   符号:   方法 getCartId()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[316,48] 找不到符号
[ERROR]   符号:   方法 getProductId()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[317,53] 找不到符号       
[ERROR]   符号:   方法 getName()
[ERROR]   位置: 类型为com.example.demo.entity.Product的变量 product
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[320,35] 找不到符号
[ERROR]   符号:   方法 getStock()
[ERROR]   位置: 类型为com.example.demo.entity.Product的变量 product
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[320,53] 找不到符号       
[ERROR]   符号:   方法 getQuantity()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[321,45] 找不到符号       
[ERROR]   符号:   方法 getCartId()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[322,48] 找不到符号       
[ERROR]   符号:   方法 getProductId()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[323,53] 找不到符号       
[ERROR]   符号:   方法 getName()
[ERROR]   位置: 类型为com.example.demo.entity.Product的变量 product
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[324,54] 找不到符号       
[ERROR]   符号:   方法 getQuantity()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[325,56] 找不到符号       
[ERROR]   符号:   方法 getStock()
[ERROR]   位置: 类型为com.example.demo.entity.Product的变量 product
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[354,60] 方法引用无效
[ERROR]   找不到符号
[ERROR]     符号:   方法 getQuantity()
[ERROR]     位置: 类 com.example.demo.entity.ShoppingCart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[358,71] 方法引用无效     
[ERROR]   找不到符号
[ERROR]     符号:   方法 getQuantity()
[ERROR]     位置: 类 com.example.demo.entity.ShoppingCart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[369,41] 找不到符号
[ERROR]   符号:   方法 getProduct()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[370,95] 找不到符号       
[ERROR]   符号:   方法 getQuantity()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[370,38] 找不到符号       
[ERROR]   符号:   方法 getProduct()
[ERROR]   位置: 类型为com.example.demo.entity.ShoppingCart的变量 cart
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ShoppingCartServiceImpl.java:[371,46] 不兼容的类型: 方 
法引用无效
[ERROR]     对于add(java.lang.Object,java.lang.Object), 找不到合适的方法
[ERROR]         方法 java.math.BigDecimal.add(java.math.BigDecimal,java.math.MathContext)不适用
[ERROR]           (参数不匹配; java.lang.Object无法转换为java.math.BigDecimal)
[ERROR]         方法 java.math.BigDecimal.add(long,long)不适用
[ERROR]           (参数不匹配; java.lang.Object无法转换为long)
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/entity/User.java:[210,16] 找不到符号
[ERROR]   符号:   变量 idCard
[ERROR]   位置: 类 com.example.demo.entity.User
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/entity/User.java:[214,13] 找不到符号
[ERROR]   符号: 变量 idCard
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/entity/User.java:[218,16] 找不到符号
[ERROR]   符号:   变量 birthDate
[ERROR]   位置: 类 com.example.demo.entity.User
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/entity/User.java:[222,13] 找不到符号
[ERROR]   符号: 变量 birthDate
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/SmartFarmSystemApplication.java:[49,13] 找不到符号
[ERROR]   符号:   变量 log
[ERROR]   位置: 类 com.example.demo.SmartFarmSystemApplication
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/SmartFarmSystemApplication.java:[70,13] 找不到符号
[ERROR]   符号:   变量 log
[ERROR]   位置: 类 com.example.demo.SmartFarmSystemApplication
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/SmartFarmSystemApplication.java:[93,13] 找不到符号
[ERROR]   符号:   变量 log
[ERROR]   位置: 类 com.example.demo.SmartFarmSystemApplication
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/SmartFarmSystemApplication.java:[103,9] 找不到符号
[ERROR]   符号:   变量 log
[ERROR]   位置: 类 com.example.demo.SmartFarmSystemApplication
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/SmartFarmSystemApplication.java:[118,13] 找不到符号
[ERROR]   符号:   变量 log
[ERROR]   位置: 类 com.example.demo.SmartFarmSystemApplication
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/SmartFarmSystemApplication.java:[131,13] 找不到符号
[ERROR]   符号:   变量 log
[ERROR]   位置: 类 com.example.demo.SmartFarmSystemApplication
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[41,20] 无法将接口 org.apache.ibatis.logging.Log中的方法 warn应用到给定类型;
[ERROR]   需要: java.lang.String
[ERROR]   找到: java.lang.String,java.lang.String
[ERROR]   原因: 实际参数列表和形式参数列表长度不同
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[47,20] 无法将接口 org.apache.ibatis.logging.Log中的方法 warn应用到给定类型;
[ERROR]   需要: java.lang.String
[ERROR]   找到: java.lang.String,java.lang.String
[ERROR]   原因: 实际参数列表和形式参数列表长度不同
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[53,20] 无法将接口 org.apache.ibatis.logging.Log中的方法 warn应用到给定类型;
[ERROR]   需要: java.lang.String
[ERROR]   找到: java.lang.String,java.lang.String
[ERROR]   原因: 实际参数列表和形式参数列表长度不同
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[62,17] 找不到符号
[ERROR]   符号:   方法 setCreateTime(java.time.LocalDateTime)
[ERROR]   位置: 类型为com.example.demo.entity.User的变量 user
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[63,17] 找不到符号
[ERROR]   符号:   方法 setUpdateTime(java.time.LocalDateTime)
[ERROR]   位置: 类型为com.example.demo.entity.User的变量 user
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[91,24] 无法将接口 org.apache.ibatis.logging.Log中的方法 warn应用到给定类型;
[ERROR]   需要: java.lang.String
[ERROR]   找到: java.lang.String,java.lang.String
[ERROR]   原因: 实际参数列表和形式参数列表长度不同
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[96,37] 找不到符号
[ERROR]   符号:   方法 getUserId()
[ERROR]   位置: 类型为com.example.demo.entity.User的变量 user
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[100,16] 无法将接口 org.apache.ibatis.logging.Log中的方法 warn应用到给定类型;
[ERROR]   需要: java.lang.String
[ERROR]   找到: java.lang.String,java.lang.String
[ERROR]   原因: 实际参数列表和形式参数列表长度不同
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[131,17] 找不到符号
[ERROR]   符号:   方法 setUpdateTime(java.time.LocalDateTime)
[ERROR]   位置: 类型为com.example.demo.entity.User的变量 user
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[144,20] 无法将接口 org.apache.ibatis.logging.Log中的方法 warn应用到给定类型;
[ERROR]   需要: java.lang.String
[ERROR]   找到: java.lang.String,java.lang.Long
[ERROR]   原因: 实际参数列表和形式参数列表长度不同
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[150,20] 无法将接口 org.apache.ibatis.logging.Log中的方法 warn应用到给定类型;
[ERROR]   需要: java.lang.String
[ERROR]   找到: java.lang.String,java.lang.Long
[ERROR]   原因: 实际参数列表和形式参数列表长度不同
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[156,17] 找不到符号
[ERROR]   符号:   方法 setUpdateTime(java.time.LocalDateTime)
[ERROR]   位置: 类型为com.example.demo.entity.User的变量 user
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[169,20] 无法将接口 org.apache.ibatis.logging.Log中的方法 warn应用到给定类型;
[ERROR]   需要: java.lang.String
[ERROR]   找到: java.lang.String,java.lang.String
[ERROR]   原因: 实际参数列表和形式参数列表长度不同
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[174,17] 找不到符号
[ERROR]   符号:   方法 setUpdateTime(java.time.LocalDateTime)
[ERROR]   位置: 类型为com.example.demo.entity.User的变量 user
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[185,30] 无法将接口 com.example.demo.mapper.UserMapper中的方法 updateLastLogin应用到给定类型;
[ERROR]   需要: java.lang.Long,java.time.LocalDateTime,java.lang.String
[ERROR]   找到: java.lang.Long,java.time.LocalDateTime
[ERROR]   原因: 实际参数列表和形式参数列表长度不同
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[194,26] 找不到符号
[ERROR]   符号:   方法 getUserList(com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.example.demo.entity.User>,java.lang.String,java.lang.String,java.lang.Integer)
[ERROR]   位置: 类型为com.example.demo.mapper.UserMapper的变量 userMapper
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[199,26] 找不到符号
[ERROR]   符号:   方法 getUsersByRole(java.lang.String)
[ERROR]   位置: 类型为com.example.demo.mapper.UserMapper的变量 userMapper
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[204,26] 找不到符号
[ERROR]   符号:   方法 getPendingFarmers()
[ERROR]   位置: 类型为com.example.demo.mapper.UserMapper的变量 userMapper
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[210,30] 无法将接口 com.example.demo.mapper.UserMapper中的方法 updateAuthStatus应用到给定类型;
[ERROR]   需要: java.lang.Long,java.lang.Integer
[ERROR]   找到: java.lang.Long,java.lang.Integer,java.lang.String,java.time.LocalDateTime
[ERROR]   原因: 实际参数列表和形式参数列表长度不同
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/UserServiceImpl.java:[316,16] 对于error(java.lang.String,java.lang.Long,java.lang.Exception), 找不到合适的方法
[ERROR]     方法 org.apache.ibatis.logging.Log.error(java.lang.String,java.lang.Throwable)不适用
[ERROR]       (实际参数列表和形式参数列表长度不同)
[ERROR]     方法 org.apache.ibatis.logging.Log.error(java.lang.String)不适用
[ERROR]       (实际参数列表和形式参数列表长度不同)
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/config/JwtAuthenticationFilter.java:[70,25] 找不到符号
[ERROR]   符号:   变量 log
[ERROR]   位置: 类 com.example.demo.config.JwtAuthenticationFilter
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/config/JwtAuthenticationFilter.java:[72,25] 找不到符号
[ERROR]   符号:   变量 log
[ERROR]   位置: 类 com.example.demo.config.JwtAuthenticationFilter
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/config/JwtAuthenticationFilter.java:[78,17] 找不到符号
[ERROR]   符号:   变量 log
[ERROR]   位置: 类 com.example.demo.config.JwtAuthenticationFilter
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/config/JwtAuthenticationFilter.java:[83,13] 找不到符号
[ERROR]   符号:   变量 log
[ERROR]   位置: 类 com.example.demo.config.JwtAuthenticationFilter
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/config/SecurityConfig.java:[91,17] 找不到符号
[ERROR]   符号: 变量 log
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/config/SecurityConfig.java:[113,13] 找不到符号
[ERROR]   符号:   变量 log
[ERROR]   位置: 类 com.example.demo.config.SecurityConfig
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/config/SecurityConfig.java:[159,17] 无法将类 org.springframework.security.config.annotation.web.AbstractRequestMatcherRegistry<C>中的方法 requestMatchers应用到给定类型;
[ERROR]   需要: org.springframework.security.web.util.matcher.RequestMatcher[]
[ERROR]   找到: java.lang.String
[ERROR]   原因: varargs 不匹配; java.lang.String无法转换为org.springframework.security.web.util.matcher.RequestMatcher
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/controller/AuthController.java:[89,24] 找不到符号
[ERROR]   符号:   方法 updateLastLoginTime(java.lang.Long)
[ERROR]   位置: 类型为com.example.demo.service.UserService的变量 userService
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/controller/AuthController.java:[275,36] 找不到符号
[ERROR]   符号:   方法 getUserById(java.lang.Long)
[ERROR]   位置: 类型为com.example.demo.service.UserService的变量 userService
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ProductCategoryServiceImpl.java:[63,64] 找不到符号
[ERROR]   符号:   方法 getCategoryId()
[ERROR]   位置: 类型为com.example.demo.entity.ProductCategory的变量 category
[ERROR]   符号:   方法 getCategoryId()
[ERROR]   位置: 类型为com.example.demo.entity.ProductCategory的变量 category
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ProductCategoryServiceImpl.java:[73,54] 找不到符号     
[ERROR]   符号:   方法 getCategoryId()
[ERROR]   位置: 类型为com.example.demo.entity.ProductCategory的变量 category
[ERROR] /C:/Users/<USER>/Desktop/trae/demo/src/main/java/com/example/demo/service/impl/ProductCategoryServiceImpl.java:[75,24] 无法将接口 org.apache.ibatis.logging.Log中的方法 warn应用到给定类型;
[ERROR]   需要: java.lang.String
[ERROR]   找到: java.lang.String,java.lang.String
[ERROR]   原因: 实际参数列表和形式参数列表长度不同
[ERROR] -> [Help 1]
[ERROR]
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR]
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoFailureException