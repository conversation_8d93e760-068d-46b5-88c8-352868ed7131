package com.example.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据用户名查询用     *
     * @param username 用户�?     * @return 用户信息
     */
    @Select("SELECT * FROM sys_user WHERE username = #{username} AND status = 1")
    User findByUsername(@Param("username") String username);

    /**
     * 根据邮箱查询用户
     *
     * @param email 邮箱
     * @return 用户信息
     */
    @Select("SELECT * FROM sys_user WHERE email = #{email} AND status = 1")
    User findByEmail(@Param("email") String email);

    /**
     * 根据手机号查询用�?     *
     * @param phone 手机�?     * @return 用户信息
     */
    @Select("SELECT * FROM sys_user WHERE phone = #{phone} AND status = 1")
    User findByPhone(@Param("phone") String phone);

    /**
     * 更新最后登录信�?     *
     * @param userId 用户ID
     * @param loginTime 登录时间
     * @param loginIp 登录IP
     */
    @Update("UPDATE sys_user SET last_login_time = #{loginTime}, last_login_ip = #{loginIp} WHERE id = #{userId}")
    void updateLastLogin(@Param("userId") Long userId,
                        @Param("loginTime") LocalDateTime loginTime,
                        @Param("loginIp") String loginIp);

    /**
     * 分页查询用户列表
     *
     * @param page 分页参数
     * @param role 角色
     * @param status 状�?     * @param keyword 关键�?     * @return 用户列表
     */
    @Select("<script>" +
            "SELECT * FROM sys_user WHERE 1=1" +
            "<if test='role != null and role != \"\">" +
            " AND role = #{role}" +
            "</if>" +
            "<if test='status != null'>" +
            " AND status = #{status}" +
            "</if>" +
            "<if test='keyword != null and keyword != \"\">" +
            " AND (username LIKE CONCAT('%', #{keyword}, '%') OR real_name LIKE CONCAT('%', #{keyword}, '%') OR email LIKE CONCAT('%', #{keyword}, '%'))" +
            "</if>" +
            " ORDER BY created_at DESC" +
            "</script>")
    IPage<User> selectUserPage(Page<User> page,
                              @Param("role") String role,
                              @Param("status") Integer status,
                              @Param("keyword") String keyword);

    /**
     * 根据角色查询用户列表
     *
     * @param role 角色
     * @return 用户列表
     */
    @Select("SELECT * FROM sys_user WHERE role = #{role} AND status = 1 ORDER BY created_at DESC")
    List<User> findByRole(@Param("role") String role);

    /**
     * 查询待审核的农户
     *
     * @return 农户列表
     */
    @Select("SELECT * FROM sys_user WHERE role = 'farmer' AND auth_status = 0 ORDER BY created_at DESC")
    List<User> findPendingFarmers();

    /**
     * 更新用户认证状�?     *
     * @param userId 用户ID
     * @param authStatus 认证状�?     */
    @Update("UPDATE sys_user SET auth_status = #{authStatus} WHERE id = #{userId}")
    void updateAuthStatus(@Param("userId") Long userId, @Param("authStatus") Integer authStatus);

    // Additional methods for compatibility with service layer
    default IPage<User> getUserList(Page<User> page, String keyword, String role, Integer status) {
        return selectUserPage(page, role, status, keyword);
    }

    default List<User> getUsersByRole(String role) {
        return findByRole(role);
    }

    default List<User> getPendingFarmers() {
        return findPendingFarmers();
    }
}
