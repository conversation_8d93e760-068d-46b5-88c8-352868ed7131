package com.example.demo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.demo.entity.Product;
import com.example.demo.entity.ShoppingCart;
import com.example.demo.mapper.ShoppingCartMapper;
import com.example.demo.service.ProductService;
import com.example.demo.service.ShoppingCartService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 购物车服务实现类
 */
@Service
@Transactional
public class ShoppingCartServiceImpl extends ServiceImpl<ShoppingCartMapper, ShoppingCart> implements ShoppingCartService {
    
    private static final Logger log = LoggerFactory.getLogger(ShoppingCartServiceImpl.class);

    @Autowired
    private ShoppingCartMapper cartMapper;

    @Autowired
    private ProductService productService;

    @Override
    public boolean addToCart(Long userId, Long productId, Integer quantity) {
        try {
            // 检查商品是否存在且可购买
            Product product = productService.findById(productId);
            if (product == null) {
                log.warn("商品不存在: {}", productId);
                return false;
            }
            
            if (product.getStatus() != 1) {
                log.warn("商品未上架: {}", productId);
                return false;
            }
            
            // 检查库存
            if (!productService.checkStock(productId, quantity)) {
                log.warn("商品库存不足: productId={}, quantity={}", productId, quantity);
                return false;
            }

            // 检查商品是否已在购物车中
            ShoppingCart existingCart = getUserCartProduct(userId, productId);
            if (existingCart != null) {
                // 更新数量
                int newQuantity = existingCart.getQuantity() + quantity;
                if (!productService.checkStock(productId, newQuantity)) {
                    log.warn("商品库存不足: productId={}, newQuantity={}", productId, newQuantity);
                    return false;
                }
                return updateCartQuantity(existingCart.getCartId(), newQuantity);
            } else {
                // 添加新商品到购物车
                ShoppingCart cart = new ShoppingCart();
                cart.setUserId(userId);
                cart.setProductId(productId);
                cart.setQuantity(quantity);
                cart.setSelected(true);
                cart.setCreateTime(LocalDateTime.now());
                cart.setUpdateTime(LocalDateTime.now());
                return save(cart);
            }
        } catch (Exception e) {
            log.error("添加商品到购物车失败", e);
            return false;
        }
    }

    @Override
    public boolean updateCartQuantity(Long cartId, Integer quantity) {
        try {
            if (quantity <= 0) {
                return removeFromCart(cartId);
            }

            ShoppingCart cart = getById(cartId);
            if (cart == null) {
                log.warn("购物车商品不存在: {}", cartId);
                return false;
            }

            // 检查库存
            if (!productService.checkStock(cart.getProductId(), quantity)) {
                log.warn("商品库存不足: productId={}, quantity={}", cart.getProductId(), quantity);
                return false;
            }

            cart.setQuantity(quantity);
            cart.setUpdateTime(LocalDateTime.now());
            return updateById(cart);
        } catch (Exception e) {
            log.error("更新购物车商品数量失败", e);
            return false;
        }
    }

    @Override
    public boolean updateCartQuantity(Long userId, Long productId, Integer quantity) {
        try {
            ShoppingCart cart = getUserCartProduct(userId, productId);
            if (cart == null) {
                log.warn("购物车中不存在该商品: userId={}, productId={}", userId, productId);
                return false;
            }
            return updateCartQuantity(cart.getCartId(), quantity);
        } catch (Exception e) {
            log.error("更新购物车商品数量失败", e);
            return false;
        }
    }

    @Override
    public boolean removeFromCart(Long cartId) {
        try {
            return removeById(cartId);
        } catch (Exception e) {
            log.error("删除购物车商品失败", e);
            return false;
        }
    }

    @Override
    public boolean removeFromCart(Long userId, Long productId) {
        try {
            return cartMapper.removeUserCartProduct(userId, productId) > 0;
        } catch (Exception e) {
            log.error("删除购物车商品失败", e);
            return false;
        }
    }

    @Override
    public boolean batchRemoveFromCart(List<Long> cartIds) {
        try {
            return removeByIds(cartIds);
        } catch (Exception e) {
            log.error("批量删除购物车商品失败", e);
            return false;
        }
    }

    @Override
    public boolean clearCart(Long userId) {
        try {
            return cartMapper.clearUserCart(userId) >= 0;
        } catch (Exception e) {
            log.error("清空用户购物车失败", e);
            return false;
        }
    }

    @Override
    public List<ShoppingCart> getUserCartList(Long userId) {
        return cartMapper.getUserCartList(userId);
    }

    @Override
    public List<ShoppingCart> getUserSelectedCartList(Long userId) {
        return cartMapper.getUserSelectedCartList(userId);
    }

    @Override
    public ShoppingCart getUserCartProduct(Long userId, Long productId) {
        return cartMapper.getUserCartProduct(userId, productId);
    }

    @Override
    public boolean setCartSelected(Long cartId, Boolean selected) {
        try {
            ShoppingCart cart = new ShoppingCart();
            cart.setCartId(cartId);
            cart.setSelected(selected);
            cart.setUpdateTime(LocalDateTime.now());
            return updateById(cart);
        } catch (Exception e) {
            log.error("设置购物车商品选中状态失败", e);
            return false;
        }
    }

    @Override
    public boolean setCartSelected(Long userId, Long productId, Boolean selected) {
        try {
            ShoppingCart cart = getUserCartProduct(userId, productId);
            if (cart == null) {
                log.warn("购物车中不存在该商品: userId={}, productId={}", userId, productId);
                return false;
            }
            return setCartSelected(cart.getCartId(), selected);
        } catch (Exception e) {
            log.error("设置购物车商品选中状态失败", e);
            return false;
        }
    }

    @Override
    public boolean batchSetCartSelected(List<Long> cartIds, Boolean selected) {
        try {
            QueryWrapper<ShoppingCart> wrapper = new QueryWrapper<>();
            wrapper.in("cart_id", cartIds);
            
            ShoppingCart cart = new ShoppingCart();
            cart.setSelected(selected);
            cart.setUpdateTime(LocalDateTime.now());
            
            return update(cart, wrapper);
        } catch (Exception e) {
            log.error("批量设置购物车商品选中状态失败", e);
            return false;
        }
    }

    @Override
    public boolean selectAllCart(Long userId, Boolean selected) {
        try {
            QueryWrapper<ShoppingCart> wrapper = new QueryWrapper<>();
            wrapper.eq("user_id", userId);
            
            ShoppingCart cart = new ShoppingCart();
            cart.setSelected(selected);
            cart.setUpdateTime(LocalDateTime.now());
            
            return update(cart, wrapper);
        } catch (Exception e) {
            log.error("全选/取消全选购物车失败", e);
            return false;
        }
    }

    @Override
    public boolean removeSelectedCart(Long userId) {
        try {
            return cartMapper.removeUserSelectedCart(userId) >= 0;
        } catch (Exception e) {
            log.error("删除用户已选中购物车商品失败", e);
            return false;
        }
    }

    @Override
    public Integer getUserCartCount(Long userId) {
        return cartMapper.getUserCartCount(userId);
    }

    @Override
    public Integer getUserSelectedCartCount(Long userId) {
        return cartMapper.getUserSelectedCartCount(userId);
    }

    @Override
    public boolean isProductInCart(Long userId, Long productId) {
        return getUserCartProduct(userId, productId) != null;
    }

    @Override
    public boolean mergeCart(Long userId, List<ShoppingCart> tempCartList) {
        try {
            if (tempCartList == null || tempCartList.isEmpty()) {
                return true;
            }

            for (ShoppingCart tempCart : tempCartList) {
                // 检查商品是否已在用户购物车中
                ShoppingCart existingCart = getUserCartProduct(userId, tempCart.getProductId());
                if (existingCart != null) {
                    // 合并数量
                    int newQuantity = existingCart.getQuantity() + tempCart.getQuantity();
                    updateCartQuantity(existingCart.getCartId(), newQuantity);
                } else {
                    // 添加到用户购物车
                    addToCart(userId, tempCart.getProductId(), tempCart.getQuantity());
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("合并购物车失败", e);
            return false;
        }
    }

    @Override
    public Object validateCart(Long userId) {
        try {
            List<ShoppingCart> cartList = getUserCartList(userId);
            List<Map<String, Object>> invalidItems = new ArrayList<>();
            
            for (ShoppingCart cart : cartList) {
                Product product = cart.getProduct();
                Map<String, Object> issue = new HashMap<>();
                
                if (product == null) {
                    issue.put("cartId", cart.getCartId());
                    issue.put("productId", cart.getProductId());
                    issue.put("issue", "商品不存在");
                    invalidItems.add(issue);
                } else if (product.getStatus() != 1) {
                    issue.put("cartId", cart.getCartId());
                    issue.put("productId", cart.getProductId());
                    issue.put("productName", product.getName());
                    issue.put("issue", "商品已下架");
                    invalidItems.add(issue);
                } else if (product.getStock() < cart.getQuantity()) {
                    issue.put("cartId", cart.getCartId());
                    issue.put("productId", cart.getProductId());
                    issue.put("productName", product.getName());
                    issue.put("requestQuantity", cart.getQuantity());
                    issue.put("availableStock", product.getStock());
                    issue.put("issue", "库存不足");
                    invalidItems.add(issue);
                }
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("valid", invalidItems.isEmpty());
            result.put("invalidItems", invalidItems);
            
            return result;
        } catch (Exception e) {
            log.error("验证购物车失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("valid", false);
            result.put("error", "验证失败");
            return result;
        }
    }

    @Override
    public Object getCartStatistics(Long userId) {
        try {
            List<ShoppingCart> cartList = getUserCartList(userId);
            List<ShoppingCart> selectedCartList = getUserSelectedCartList(userId);
            
            Map<String, Object> statistics = new HashMap<>();
            
            // 总商品数量
            int totalQuantity = cartList.stream().mapToInt(ShoppingCart::getQuantity).sum();
            statistics.put("totalQuantity", totalQuantity);
            
            // 已选中商品数量
            int selectedQuantity = selectedCartList.stream().mapToInt(ShoppingCart::getQuantity).sum();
            statistics.put("selectedQuantity", selectedQuantity);
            
            // 总商品种类数
            statistics.put("totalItems", cartList.size());
            
            // 已选中商品种类数
            statistics.put("selectedItems", selectedCartList.size());
            
            // 已选中商品总金额
            BigDecimal totalAmount = selectedCartList.stream()
                    .filter(cart -> cart.getProduct() != null)
                    .map(cart -> cart.getProduct().getPrice().multiply(BigDecimal.valueOf(cart.getQuantity())))
                    .reduce(BigDecimal.ZERO, (a, b) -> a.add(b));
            statistics.put("totalAmount", totalAmount);
            
            return statistics;
        } catch (Exception e) {
            log.error("获取购物车统计信息失败", e);
            return new HashMap<>();
        }
    }
}