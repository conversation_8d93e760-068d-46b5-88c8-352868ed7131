# Spring Boot 应用配置
spring:
  # 应用基本信息
  application:
    name: smart-farm-system
  
  # 环境配置
  profiles:
    active: dev
  
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************************
    username: root
    password: 123456
    # 连接池配置
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: SmartFarmHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
  
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
      enabled: true
  
  # 国际化配置
  messages:
    basename: i18n/messages
    encoding: UTF-8
    cache-duration: 3600
  
  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 600000
      cache-null-values: false

# MyBatis-Plus配置
mybatis-plus:
  # 实体类包路径
  type-aliases-package: com.example.demo.entity
  # Mapper XML文件路径
  mapper-locations: classpath*:/mapper/**/*.xml
  # 全局配置
  global-config:
    # 数据库相关配置
    db-config:
      # 主键类型（AUTO:数据库ID自增，ASSIGN_ID:雪花算法）
      id-type: AUTO
      # 字段策略（IGNORED:忽略判断，NOT_NULL:非NULL判断，NOT_EMPTY:非空判断）
      field-strategy: NOT_EMPTY
      # 逻辑删除字段名
      logic-delete-field: deleted
      # 逻辑删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0
      # 数据库表前缀
      table-prefix: 
  # 配置
  configuration:
    # 是否开启自动驼峰命名规则映射
    map-underscore-to-camel-case: true
    # 开启Mybatis二级缓存
    cache-enabled: false
    # 配置JdbcTypeForNull
    jdbc-type-for-null: null
    # 配置默认的执行器
    default-executor-type: reuse
    # 配置默认语句超时时间
    default-statement-timeout: 25000
    # 日志实现
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  # Tomcat配置
  tomcat:
    uri-encoding: UTF-8
    max-threads: 200
    min-spare-threads: 10
    max-connections: 8192
    accept-count: 100
    connection-timeout: 20000

# 日志配置
logging:
  level:
    root: INFO
    com.example.demo: DEBUG
    com.example.demo.mapper: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
  file:
    name: logs/smart-farm-system.log
    max-size: 100MB
    max-history: 30

# JWT配置
jwt:
  # 密钥（生产环境请使用更复杂的密钥）
  secret: smartFarmSystemJwtSecretKey2024
  # 访问令牌过期时间（秒）
  access-token-expiration: 7200
  # 刷新令牌过期时间（秒）
  refresh-token-expiration: 604800
  # 令牌前缀
  token-prefix: Bearer 
  # 请求头名称
  header-name: Authorization

# 应用自定义配置
app:
  # 文件上传配置
  upload:
    # 上传路径
    path: uploads/
    # 允许的文件类型
    allowed-types: jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx
    # 最大文件大小（MB）
    max-size: 10
  
  # 短信配置
  sms:
    # 阿里云短信配置
    aliyun:
      access-key-id: your-access-key-id
      access-key-secret: your-access-key-secret
      sign-name: 智慧农场
      template-code: SMS_123456789
  
  # 邮件配置
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-email-password
    from: <EMAIL>
  
  # 支付配置
  payment:
    # 支付宝配置
    alipay:
      app-id: your-alipay-app-id
      private-key: your-alipay-private-key
      public-key: your-alipay-public-key
      gateway-url: https://openapi.alipay.com/gateway.do
      notify-url: http://your-domain.com/api/payment/alipay/notify
      return-url: http://your-domain.com/payment/success
    
    # 微信支付配置
    wechat:
      app-id: your-wechat-app-id
      mch-id: your-wechat-mch-id
      api-key: your-wechat-api-key
      notify-url: http://your-domain.com/api/payment/wechat/notify
  
  # 积分配置
  points:
    # 注册赠送积分
    register-bonus: 100
    # 每日签到积分
    daily-checkin: 10
    # 积分兑换比例（1元=100积分）
    exchange-rate: 100
  
  # 安全配置
  security:
    # 密码加密盐值
    password-salt: smartFarmPasswordSalt2024
    # 登录失败最大次数
    max-login-attempts: 5
    # 账户锁定时间（分钟）
    account-lock-duration: 30
  
  # 业务配置
  business:
    # 订单自动取消时间（分钟）
    order-auto-cancel-minutes: 30
    # 订单自动确认收货时间（天）
    order-auto-confirm-days: 7
    # 库存预警阈值
    stock-warning-threshold: 10

# 管理和监控
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# Swagger文档配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    operations-sorter: method
    tags-sorter: alpha
  packages-to-scan: com.example.demo.controller
  paths-to-match: /api/**