<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="0b9bc484-6bbb-4ef9-b5bd-2236529d09e4" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\JAVA项目\Maven\apache-maven-3.6.3" />
        <option name="localRepository" value="D:\JAVA项目\Maven\localRepository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\JAVA项目\Maven\apache-maven-3.6.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="32Be0oY00cwsGzRMYv91EjJq0wC" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.DemoApplication.executor": "Run",
    "Spring Boot.SmartFarmSystemApplication.executor": "Run",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/trae",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "工件",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true",
    "应用程序.DemoApplication.executor": "Run"
  }
}]]></component>
  <component name="RunManager" selected="Spring Boot.SmartFarmSystemApplication">
    <configuration name="DemoApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.example.demo.DemoApplication" />
      <module name="smart-farm-system" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.example.demo.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DemoApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="smart-farm-system" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.demo.DemoApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.example.demo.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SmartFarmSystemApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="smart-farm-system" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.demo.SmartFarmSystemApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.example.demo.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.SmartFarmSystemApplication" />
        <item itemvalue="Spring Boot.DemoApplication" />
        <item itemvalue="应用程序.DemoApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="0b9bc484-6bbb-4ef9-b5bd-2236529d09e4" name="更改" comment="" />
      <created>1756898173671</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1756898173671</updated>
      <workItem from="1756898174966" duration="134000" />
      <workItem from="1756898356830" duration="3375000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>