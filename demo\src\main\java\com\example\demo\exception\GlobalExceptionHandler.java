package com.example.demo.exception;

import com.example.demo.common.ApiResponse;
import com.example.demo.common.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.sql.SQLException;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.OK)
    public ApiResponse<Void> handleBusinessException(BusinessException e, HttpServletRequest request) {
        log.warn("业务异常: {} - {}", e.getCode(), e.getMessage());
        return ApiResponse.error(e.getCode(), e.getMessage())
                .requestId(getRequestId(request));
    }

    /**
     * 处理参数验证异常 - @Valid 注解验证失败
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletRequest request) {
        log.warn("参数验证异常: {}", e.getMessage());
        
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        
        return ApiResponse.error(ResultCode.VALIDATION_FAILED.getCode(), errorMessage)
                .requestId(getRequestId(request));
    }

    /**
     * 处理参数绑定异常 - @ModelAttribute 验证失败
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleBindException(BindException e, HttpServletRequest request) {
        log.warn("参数绑定异常: {}", e.getMessage());
        
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        
        return ApiResponse.error(ResultCode.VALIDATION_FAILED.getCode(), errorMessage)
                .requestId(getRequestId(request));
    }

    /**
     * 处理约束验证异常 - @Validated 注解验证失败
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleConstraintViolationException(ConstraintViolationException e, HttpServletRequest request) {
        log.warn("约束验证异常: {}", e.getMessage());
        
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        String errorMessage = violations.stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        
        return ApiResponse.error(ResultCode.VALIDATION_FAILED.getCode(), errorMessage)
                .requestId(getRequestId(request));
    }

    /**
     * 处理缺少请求参数异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleMissingServletRequestParameterException(MissingServletRequestParameterException e, HttpServletRequest request) {
        log.warn("缺少请求参数异常: {}", e.getMessage());
        String message = String.format("缺少必需的请求参数: %s", e.getParameterName());
        return ApiResponse.error(ResultCode.BAD_REQUEST.getCode(), message)
                .requestId(getRequestId(request));
    }

    /**
     * 处理参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e, HttpServletRequest request) {
        log.warn("参数类型不匹配异常: {}", e.getMessage());
        String message = String.format("参数 %s 类型不匹配，期望类型: %s", e.getName(), e.getRequiredType().getSimpleName());
        return ApiResponse.error(ResultCode.BAD_REQUEST.getCode(), message)
                .requestId(getRequestId(request));
    }

    /**
     * 处理HTTP消息不可读异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleHttpMessageNotReadableException(HttpMessageNotReadableException e, HttpServletRequest request) {
        log.warn("HTTP消息不可读异常: {}", e.getMessage());
        return ApiResponse.error(ResultCode.BAD_REQUEST.getCode(), "请求体格式错误")
                .requestId(getRequestId(request));
    }

    /**
     * 处理请求方法不支持异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public ApiResponse<Void> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e, HttpServletRequest request) {
        log.warn("请求方法不支持异常: {}", e.getMessage());
        String message = String.format("不支持的请求方法: %s", e.getMethod());
        return ApiResponse.error(ResultCode.METHOD_NOT_ALLOWED.getCode(), message)
                .requestId(getRequestId(request));
    }

    /**
     * 处理媒体类型不支持异常
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    @ResponseStatus(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
    public ApiResponse<Void> handleHttpMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException e, HttpServletRequest request) {
        log.warn("媒体类型不支持异常: {}", e.getMessage());
        return ApiResponse.error(ResultCode.UNSUPPORTED_MEDIA_TYPE.getCode(), "不支持的媒体类型")
                .requestId(getRequestId(request));
    }

    /**
     * 处理文件上传大小超出限制异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e, HttpServletRequest request) {
        log.warn("文件上传大小超出限制异常: {}", e.getMessage());
        return ApiResponse.error(ResultCode.FILE_SIZE_EXCEEDED.getCode(), "文件大小超出限制")
                .requestId(getRequestId(request));
    }

    /**
     * 处理404异常
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ApiResponse<Void> handleNoHandlerFoundException(NoHandlerFoundException e, HttpServletRequest request) {
        log.warn("404异常: {}", e.getMessage());
        return ApiResponse.error(ResultCode.NOT_FOUND.getCode(), "请求的资源不存在")
                .requestId(getRequestId(request));
    }

    /**
     * 处理认证异常
     */
    @ExceptionHandler(AuthenticationException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public ApiResponse<Void> handleAuthenticationException(AuthenticationException e, HttpServletRequest request) {
        log.warn("认证异常: {}", e.getMessage());
        
        if (e instanceof BadCredentialsException) {
            return ApiResponse.error(ResultCode.USER_PASSWORD_ERROR)
                    .requestId(getRequestId(request));
        } else if (e instanceof DisabledException) {
            return ApiResponse.error(ResultCode.USER_ACCOUNT_DISABLED)
                    .requestId(getRequestId(request));
        } else if (e instanceof LockedException) {
            return ApiResponse.error(ResultCode.USER_ACCOUNT_LOCKED)
                    .requestId(getRequestId(request));
        }
        
        return ApiResponse.error(ResultCode.UNAUTHORIZED.getCode(), "认证失败")
                .requestId(getRequestId(request));
    }

    /**
     * 处理访问拒绝异常
     */
    @ExceptionHandler(AccessDeniedException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public ApiResponse<Void> handleAccessDeniedException(AccessDeniedException e, HttpServletRequest request) {
        log.warn("访问拒绝异常: " + e.getMessage());
        return ApiResponse.error(ResultCode.FORBIDDEN.getCode(), "访问被拒绝")
                .requestId(getRequestId(request));
    }

    /**
     * 处理数据库异常
     */
    @ExceptionHandler(SQLException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Void> handleSQLException(SQLException e, HttpServletRequest request) {
        log.error("数据库异常: {}", e.getMessage(), e);
        return ApiResponse.error(ResultCode.DATABASE_ERROR)
                .requestId(getRequestId(request));
    }

    /**
     * 处理数据完整性约束违反异常
     */
    @ExceptionHandler(DataIntegrityViolationException.class)
    @ResponseStatus(HttpStatus.CONFLICT)
    public ApiResponse<Void> handleDataIntegrityViolationException(DataIntegrityViolationException e, HttpServletRequest request) {
        log.warn("数据完整性约束违反异常: {}", e.getMessage());
        return ApiResponse.error(ResultCode.DATA_INTEGRITY_VIOLATION)
                .requestId(getRequestId(request));
    }

    /**
     * 处理重复键异常
     */
    @ExceptionHandler(DuplicateKeyException.class)
    @ResponseStatus(HttpStatus.CONFLICT)
    public ApiResponse<Void> handleDuplicateKeyException(DuplicateKeyException e, HttpServletRequest request) {
        log.warn("重复键异常: {}", e.getMessage());
        return ApiResponse.error(ResultCode.DUPLICATE_KEY_ERROR)
                .requestId(getRequestId(request));
    }

    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Void> handleNullPointerException(NullPointerException e, HttpServletRequest request) {
        log.error("空指针异常: {}", e.getMessage(), e);
        return ApiResponse.<Void>error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "系统内部错误")
                .requestId(getRequestId(request));
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleIllegalArgumentException(IllegalArgumentException e, HttpServletRequest request) {
        log.warn("非法参数异常: {}", e.getMessage());
        return ApiResponse.<Void>error(ResultCode.BAD_REQUEST.getCode(), e.getMessage())
                .requestId(getRequestId(request));
    }

    /**
     * 处理非法状态异常
     */
    @ExceptionHandler(IllegalStateException.class)
    @ResponseStatus(HttpStatus.CONFLICT)
    public ApiResponse<Void> handleIllegalStateException(IllegalStateException e, HttpServletRequest request) {
        log.warn("非法状态异常: {}", e.getMessage());
        return ApiResponse.<Void>error(ResultCode.CONFLICT.getCode(), e.getMessage())
                .requestId(getRequestId(request));
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Void> handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        log.error("运行时异常: {}", e.getMessage(), e);
        return ApiResponse.<Void>error(ResultCode.INTERNAL_SERVER_ERROR)
                .requestId(getRequestId(request));
    }

    /**
     * 处理其他所有异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Void> handleException(Exception e, HttpServletRequest request) {
        log.error("未知异常: {}", e.getMessage(), e);
        return ApiResponse.<Void>error(ResultCode.INTERNAL_SERVER_ERROR)
                .requestId(getRequestId(request));
    }

    /**
     * 获取请求ID（用于链路追踪）
     */
    private String getRequestId(HttpServletRequest request) {
        String requestId = request.getHeader("X-Request-ID");
        if (requestId == null || requestId.trim().isEmpty()) {
            requestId = request.getHeader("X-Trace-ID");
        }
        return requestId;
    }
}