package com.example.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.demo.entity.ShoppingCart;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 购物车Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface ShoppingCartMapper extends BaseMapper<ShoppingCart> {

    /**
     * 查询用户购物车列表（带商品信息）
     *
     * @param userId 用户ID
     * @return 购物车列    */
    @Select("SELECT sc.*, p.name as product_name, p.price, p.image, p.unit, p.stock, " +
            "u.real_name as farmer_name, (sc.quantity * p.price) as subtotal " +
            "FROM shopping_cart sc " +
            "LEFT JOIN product p ON sc.product_id = p.id " +
            "LEFT JOIN sys_user u ON p.farmer_id = u.id " +
            "WHERE sc.user_id = #{userId} " +
            "ORDER BY sc.created_at DESC")
    List<ShoppingCart> findByUserId(@Param("userId") Long userId);

    /**
     * 查询用户已选中的购物车商品
     *
     * @param userId 用户ID
     * @return 购物车列�?     */
    @Select("SELECT sc.*, p.name as product_name, p.price, p.image, p.unit, p.stock, " +
            "u.real_name as farmer_name, (sc.quantity * p.price) as subtotal " +
            "FROM shopping_cart sc " +
            "LEFT JOIN product p ON sc.product_id = p.id " +
            "LEFT JOIN sys_user u ON p.farmer_id = u.id " +
            "WHERE sc.user_id = #{userId} AND sc.selected = 1 " +
            "ORDER BY sc.created_at DESC")
    List<ShoppingCart> findSelectedByUserId(@Param("userId") Long userId);

    /**
     * 查询用户购物车中指定商品
     *
     * @param userId 用户ID
     * @param productId 商品ID
     * @return 购物车项
     */
    @Select("SELECT * FROM shopping_cart WHERE user_id = #{userId} AND product_id = #{productId}")
    ShoppingCart findByUserIdAndProductId(@Param("userId") Long userId, @Param("productId") Long productId);

    /**
     * 删除用户购物车中的商�?     *
     * @param userId 用户ID
     * @param productId 商品ID
     * @return 影响行数
     */
    @Delete("DELETE FROM shopping_cart WHERE user_id = #{userId} AND product_id = #{productId}")
    int deleteByUserIdAndProductId(@Param("userId") Long userId, @Param("productId") Long productId);

    /**
     * 清空用户购物�?     *
     * @param userId 用户ID
     * @return 影响行数
     */
    @Delete("DELETE FROM shopping_cart WHERE user_id = #{userId}")
    int deleteByUserId(@Param("userId") Long userId);

    /**
     * 删除用户已选中的购物车商品
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    @Delete("DELETE FROM shopping_cart WHERE user_id = #{userId} AND selected = 1")
    int deleteSelectedByUserId(@Param("userId") Long userId);

    /**
     * 统计用户购物车商品数�?     *
     * @param userId 用户ID
     * @return 商品数量
     */
    @Select("SELECT COALESCE(SUM(quantity), 0) FROM shopping_cart WHERE user_id = #{userId}")
    Integer countByUserId(@Param("userId") Long userId);

    /**
     * 统计用户已选中购物车商品数�?     *
     * @param userId 用户ID
     * @return 商品数量
     */
    @Select("SELECT COALESCE(SUM(quantity), 0) FROM shopping_cart WHERE user_id = #{userId} AND selected = 1")
    Integer countSelectedByUserId(@Param("userId") Long userId);

    // Additional methods for compatibility with service layer
    default List<ShoppingCart> getUserCartList(Long userId) {
        return findByUserId(userId);
    }

    default List<ShoppingCart> getUserSelectedCartList(Long userId) {
        return findSelectedByUserId(userId);
    }

    default ShoppingCart getUserCartProduct(Long userId, Long productId) {
        return findByUserIdAndProductId(userId, productId);
    }

    default int removeUserCartProduct(Long userId, Long productId) {
        return deleteByUserIdAndProductId(userId, productId);
    }

    default int clearUserCart(Long userId) {
        return deleteByUserId(userId);
    }

    default int removeUserSelectedCart(Long userId) {
        return deleteSelectedByUserId(userId);
    }

    default Integer getUserCartCount(Long userId) {
        return countByUserId(userId);
    }

    default Integer getUserSelectedCartCount(Long userId) {
        return countSelectedByUserId(userId);
    }
}
